import { oft } from "@layerzerolabs/oft-v2-solana-sdk";
import { publicKey, transactionBuilder } from "@metaplex-foundation/umi";
import { createUmi } from "@metaplex-foundation/umi-bundle-defaults";
import { addressToBytes32 } from "@layerzerolabs/lz-v2-utilities";
import { walletAdapterIdentity } from "@metaplex-foundation/umi-signer-wallet-adapters";
import { Connection } from "@solana/web3.js";
import { findAssociatedTokenPda, setComputeUnitLimit, setComputeUnitPrice } from '@metaplex-foundation/mpl-toolbox';
import bs58 from 'bs58';
import { parseUnits } from 'viem';
import { BridgeConfig, getBridgeConfig, getNetworkRpcUrl, getTokenDecimals, CONTRACT_ADDRESSES } from '../utils/bridgeConfig';
import { Network } from '../components/NetworkSelector';

export interface QuoteResult {
  nativeFee: string;
  receiveAmount: string;
  gasPrice?: string;
}

export interface BridgeResult {
  txHash: string;
  layerZeroScanLink?: string;
}

export class BridgeService {
  private config: BridgeConfig;

  constructor(fromNetwork: Network, toNetwork: Network) {
    this.config = getBridgeConfig(fromNetwork, toNetwork);
  }

  isSupported(): boolean {
    return this.config.isSupported;
  }

  getBridgeType(): string {
    return this.config.bridgeType;
  }

  async quoteBridge(
    amount: string,
    recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string
  ): Promise<QuoteResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.quoteSolanaToEvm(amount, recipientAddress, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.quoteEvmToSolana(amount, recipientAddress, ethAddress);
      case 'EVM_TO_EVM':
        return this.quoteEvmToEvm(amount, recipientAddress, ethAddress);
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  async executeBridge(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    solanaWallet?: any,
    ethAddress?: string,
    writeContract?: any
  ): Promise<BridgeResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.executeSolanaToEvm(amount, recipientAddress, nativeFee, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.executeEvmToSolana(amount, recipientAddress, nativeFee, ethAddress, writeContract);
      case 'EVM_TO_EVM':
        return this.executeEvmToEvm(amount, recipientAddress, nativeFee, ethAddress, writeContract);
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  private async quoteSolanaToEvm(
    amount: string,
    recipientAddress: string,
    solanaWallet: any
  ): Promise<QuoteResult> {
    if (!CONTRACT_ADDRESSES.solana.mint || !CONTRACT_ADDRESSES.solana.escrow ||
      !CONTRACT_ADDRESSES.solana.program || !this.config.toContractAddress) {
      throw new Error("Missing Solana or target EVM contract configuration");
    }

    const connection = new Connection(getNetworkRpcUrl('solana'));
    const umi = createUmi(connection).use(walletAdapterIdentity(solanaWallet));

    const solanaDecimals = getTokenDecimals('solana');
    const targetDecimals = getTokenDecimals(this.config.toNetwork.id);
    const amountInLamports = BigInt(parseFloat(amount) * Math.pow(10, solanaDecimals));

    const recipientAddressBytes32 = addressToBytes32(recipientAddress);

    const sendParam = {
      dstEid: this.config.toEndpointId,
      to: recipientAddressBytes32,
      amountLD: amountInLamports,
      minAmountLD: amountInLamports,
      extraOptions: new Uint8Array(),
      composeMsg: new Uint8Array(),
      oftCmd: new Uint8Array(),
    };

    const quoteResult = await oft.quote(umi, {
      oftStore: publicKey(CONTRACT_ADDRESSES.solana.store!),
      sendParam,
      payInLzToken: false,
    });

    const nativeFeeInSol = Number(quoteResult.nativeFee) / 1e9;
    const receiveAmountInTargetDecimals = parseFloat(amount); // 1:1 for now

    return {
      nativeFee: quoteResult.nativeFee.toString(),
      receiveAmount: receiveAmountInTargetDecimals.toFixed(6),
    };
  }

  private async quoteEvmToSolana(
    amount: string,
    recipientAddress: string,
    ethAddress: string
  ): Promise<QuoteResult> {
    // For now, return a mock quote for EVM to Solana
    // This would need to be implemented with the actual EVM OFT contract
    const mockFee = "0.001"; // ETH
    const receiveAmount = (parseFloat(amount) * 0.999).toFixed(6); // Mock 0.1% fee

    return {
      nativeFee: parseUnits(mockFee, 18).toString(),
      receiveAmount,
      gasPrice: "20000000000", // 20 gwei
    };
  }

  private async quoteEvmToEvm(
    amount: string,
    recipientAddress: string,
    ethAddress: string
  ): Promise<QuoteResult> {
    // For now, return a mock quote for EVM to EVM
    // This would need to be implemented with the actual EVM OFT contracts
    const mockFee = "0.002"; // ETH
    const receiveAmount = (parseFloat(amount) * 0.998).toFixed(6); // Mock 0.2% fee

    return {
      nativeFee: parseUnits(mockFee, 18).toString(),
      receiveAmount,
      gasPrice: "25000000000", // 25 gwei
    };
  }

  private async executeSolanaToEvm(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    solanaWallet: any
  ): Promise<BridgeResult> {
    if (!CONTRACT_ADDRESSES.solana.mint || !CONTRACT_ADDRESSES.solana.escrow ||
      !CONTRACT_ADDRESSES.solana.program) {
      throw new Error("Missing Solana contract configuration");
    }

    const connection = new Connection(getNetworkRpcUrl('solana'));
    const umi = createUmi(connection).use(walletAdapterIdentity(solanaWallet));

    const solanaDecimals = getTokenDecimals('solana');
    const amountInLamports = BigInt(parseFloat(amount) * Math.pow(10, solanaDecimals));

    const recipientAddressBytes32 = addressToBytes32(recipientAddress);

    const sendParam = {
      dstEid: this.config.toEndpointId,
      to: recipientAddressBytes32,
      amountLD: amountInLamports,
      minAmountLD: amountInLamports,
      extraOptions: new Uint8Array(),
      composeMsg: new Uint8Array(),
      oftCmd: new Uint8Array(),
    };

    const [ataAddress] = findAssociatedTokenPda(umi, {
      mint: publicKey(CONTRACT_ADDRESSES.solana.mint),
      owner: umi.identity.publicKey,
    });

    let transaction = transactionBuilder()
      .add(setComputeUnitLimit(umi, { units: 300_000 }))
      .add(setComputeUnitPrice(umi, { microLamports: 1_000_000 }))
      .add(
        oft.send(umi, {
          oftStore: publicKey(CONTRACT_ADDRESSES.solana.store!),
          from: ataAddress,
          sendParam,
          fee: {
            nativeFee: BigInt(nativeFee),
            lzTokenFee: 0n,
          },
          extraOptions: new Uint8Array(),
          composeMsg: new Uint8Array(),
          oftCmd: new Uint8Array(),
        })
      );

    const result = await transaction.sendAndConfirm(umi);
    const signature = bs58.encode(result.signature);

    return {
      txHash: signature,
      layerZeroScanLink: `https://layerzeroscan.com/tx/${signature}`,
    };
  }

  private async executeEvmToSolana(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    ethAddress: string,
    writeContract: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual EVM OFT contract
    throw new Error("EVM to Solana bridging is not yet implemented");
  }

  private async executeEvmToEvm(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    ethAddress: string,
    writeContract: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual EVM OFT contracts
    throw new Error("EVM to EVM bridging is not yet implemented");
  }
}
