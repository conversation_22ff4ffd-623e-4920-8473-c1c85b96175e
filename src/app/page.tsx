"use client";
import { useState, useEffect } from "react";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

export default function Home() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <Providers>
      <div className="min-h-screen bg-white flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8 relative">
        {/* USDUC Image */}
        <div className="absolute left-1/2 transform -translate-x-1/2 top-8">
          <img
            src="/usduc.jpg"
            alt="USDUC"
            className="w-48 md:w-64 h-auto object-contain rounded-lg shadow-lg"
          />
        </div>

        {/* Main Content */}
        <div className="w-full relative mt-32 md:mt-40">
          <BridgeInterface />
        </div>
      </div>
    </Providers>
  );
}
