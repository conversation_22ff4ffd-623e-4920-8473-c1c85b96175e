"use client";
import { useState } from "react";

export interface Network {
  id: string;
  name: string;
  icon: string;
  chainId?: number;
}

export const NETWORKS: Network[] = [
  {
    id: 'solana',
    name: '<PERSON><PERSON>',
    icon: '/solana-sol-logo.svg'
  },
  {
    id: 'ethereum',
    name: 'Ethereum',
    icon: '/ethereum-eth-logo.svg',
    chainId: 1
  },
  {
    id: 'base',
    name: 'Base',
    icon: '/Base_Symbol_Blue.svg',
    chainId: 8453
  }
];

interface NetworkSelectorProps {
  selectedNetwork: Network;
  onNetworkChange: (network: Network) => void;
  excludeNetwork?: Network;
  label: string;
}

export default function NetworkSelector({ 
  selectedNetwork, 
  onNetworkChange, 
  excludeNetwork,
  label 
}: NetworkSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const availableNetworks = NETWORKS.filter(
    network => !excludeNetwork || network.id !== excludeNetwork.id
  );

  return (
    <div className="relative">
      <label className="block text-black text-sm font-medium mb-2">{label}</label>
      
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-3 bg-white border-2 border-[#2b72d6] rounded-lg hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <img 
            src={selectedNetwork.icon} 
            alt={selectedNetwork.name} 
            className="w-6 h-6" 
          />
          <span className="text-black font-medium">{selectedNetwork.name}</span>
        </div>
        <svg 
          className={`w-5 h-5 text-black transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border-2 border-[#2b72d6] rounded-lg shadow-lg z-10">
          {availableNetworks.map((network) => (
            <button
              key={network.id}
              onClick={() => {
                onNetworkChange(network);
                setIsOpen(false);
              }}
              className="w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors first:rounded-t-lg last:rounded-b-lg"
            >
              <img 
                src={network.icon} 
                alt={network.name} 
                className="w-6 h-6" 
              />
              <span className="text-black font-medium">{network.name}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
