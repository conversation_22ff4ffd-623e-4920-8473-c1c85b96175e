"use client";
import { useWallet } from "@solana/wallet-adapter-react";
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useConnect, useDisconnect } from "wagmi";

import { useState, useEffect, useCallback, useMemo } from "react";


import { Connection, PublicKey } from "@solana/web3.js";
import { getAssociatedTokenAddress, getAccount } from '@solana/spl-token';
import { useReadContract } from 'wagmi';
import { erc20Abi } from 'viem';
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui";
import NetworkSelector, { Network, NETWORKS } from "./NetworkSelector";
import { BridgeService } from "../services/bridgeService";

const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
const SOLANA_OFT_MINT_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS;
const SOLANA_ESCROW_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS;
const SOLANA_PROGRAM_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS;
const ETHEREUM_OFT_ADDRESS = process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS;
const DEFAULT_AMOUNT = parseFloat(process.env.NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT || "0.1");


const SOLANA_TOKEN_DECIMALS = 6;
const ETHEREUM_TOKEN_DECIMALS = 18;

const getLayerZeroScanLink = (txHash: string, isTestnet: boolean = false): string => {
  const baseUrl = isTestnet ? 'https://testnet.layerzeroscan.com' : 'https://layerzeroscan.com';
  return `${baseUrl}/tx/${txHash}`;
};



const isValidEthereumAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

const isValidSolanaAddress = (address: string): boolean => {
  try {
    new PublicKey(address);
    return true;
  } catch {
    return false;
  }
};



interface BridgeState {
  isLoading: boolean;
  error: string | null;
  txHash: string | null;
  nativeFee: bigint | null;
  receiveAmount: string | null;
  solanaBalance: string | null;
  ethereumBalance: string | null;
  layerZeroScanLink: string | null;
  gasPrice: { maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null;
  customEthAddress: string;
  customSolanaAddress: string;
}

interface Transaction {
  hash: string;
  timestamp: number;
  fromChain: 'solana' | 'ethereum';
  toChain: 'solana' | 'ethereum';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  layerZeroScanLink?: string;
}

export default function BridgeInterface() {
  const solanaWallet = useWallet();

  const { address: ethAddress, isConnected: isEthConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { connect, connectors, isPending: isConnectPending } = useConnect();

  const [isClient, setIsClient] = useState(false);
  const [isEthWalletModalOpen, setIsEthWalletModalOpen] = useState(false);
  const [amount, setAmount] = useState(DEFAULT_AMOUNT.toString());
  const [fromNetwork, setFromNetwork] = useState<Network>(NETWORKS[0]); // Solana
  const [toNetwork, setToNetwork] = useState<Network>(NETWORKS[1]); // Ethereum



  // Bridge service instance
  const bridgeService = useMemo(() => new BridgeService(fromNetwork, toNetwork), [fromNetwork, toNetwork]);

  const [bridgeState, setBridgeState] = useState<BridgeState>({
    isLoading: false,
    error: null,
    txHash: null,
    nativeFee: null,
    receiveAmount: null,
    solanaBalance: null,
    ethereumBalance: null,
    layerZeroScanLink: null,
    gasPrice: null,
    customEthAddress: '',
    customSolanaAddress: '',
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isHistoryVisible, setIsHistoryVisible] = useState(false);



  const { data: ethereumBalanceRaw } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: erc20Abi,
    functionName: 'balanceOf',
    args: [ethAddress as `0x${string}`],
    query: {
      enabled: !!ethAddress && !!ETHEREUM_OFT_ADDRESS && isEthConnected,
    },
  });

  const { writeContract: writeOftContract, data: ethTxHash, isPending: isEthTxPending, error: ethTxError } = useWriteContract();
  const { isSuccess: isEthTxSuccess } = useWaitForTransactionReceipt({
    hash: ethTxHash,
  });

  const fetchSolanaBalance = useCallback(async () => {
    if (!solanaWallet.publicKey || !SOLANA_OFT_MINT_ADDRESS || !solanaWallet.connected) {
      return null;
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mintPublicKey = new PublicKey(SOLANA_OFT_MINT_ADDRESS);
      const ownerPublicKey = new PublicKey(solanaWallet.publicKey.toString());

      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPublicKey,
        ownerPublicKey
      );

      const tokenAccountInfo = await getAccount(connection, associatedTokenAddress);
      const balance = Number(tokenAccountInfo.amount) / Math.pow(10, SOLANA_TOKEN_DECIMALS);
      return balance.toFixed(6);
    } catch (error) {
      console.error("Error fetching Solana balance:", error);
      return "0";
    }
  }, [solanaWallet.publicKey, solanaWallet.connected]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    if (solanaWallet.connected && solanaWallet.publicKey) {
      fetchSolanaBalance().then(balance => {
        setBridgeState(prev => ({ ...prev, solanaBalance: balance }));
      });
    } else {
      setBridgeState(prev => ({ ...prev, solanaBalance: null }));
    }
  }, [isClient, solanaWallet.connected, solanaWallet.publicKey, fetchSolanaBalance]);

  useEffect(() => {
    if (!isClient) return;

    if (ethereumBalanceRaw && isEthConnected) {
      const ethBalance = (Number(ethereumBalanceRaw) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS)).toFixed(6);
      setBridgeState(prev => ({ ...prev, ethereumBalance: ethBalance }));
    } else {
      setBridgeState(prev => ({ ...prev, ethereumBalance: null }));
    }
  }, [isClient, ethereumBalanceRaw, isEthConnected]);

  useEffect(() => {
    if (ethTxHash && isEthTxSuccess) {
      const layerZeroScanLink = getLayerZeroScanLink(ethTxHash, false);

      const newTransaction: Transaction = {
        hash: ethTxHash,
        timestamp: Date.now(),
        fromChain: 'ethereum',
        toChain: 'solana',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash: ethTxHash,
        layerZeroScanLink,
        isLoading: false
      }));
    }
  }, [ethTxHash, isEthTxSuccess, amount]);

  useEffect(() => {
    if (isEthTxPending) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));
    }
  }, [isEthTxPending]);

  useEffect(() => {
    if (ethTxError) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: ethTxError.message || "Transaction failed"
      }));
    }
  }, [ethTxError]);

  // Add effect to reset button state when amount changes
  useEffect(() => {
    setBridgeState(prev => ({
      ...prev,
      nativeFee: null,
      txHash: null,
      layerZeroScanLink: null,
      error: null,
    }));
  }, [amount]);

  const resetBridgeState = useCallback(() => {
    setBridgeState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      txHash: null,
      nativeFee: null,
      receiveAmount: null,
      layerZeroScanLink: null,
      gasPrice: null,
    }));
  }, []);

  const validateInputs = useCallback(() => {
    if (!SOLANA_OFT_MINT_ADDRESS || !SOLANA_ESCROW_ADDRESS || !SOLANA_PROGRAM_ADDRESS || !ETHEREUM_OFT_ADDRESS) {
      throw new Error("Missing environment variables. Please check your .env.local file.");
    }

    const isSolanaToEvm = fromNetwork.id === 'solana' && (toNetwork.id === 'ethereum' || toNetwork.id === 'base');
    const isEvmToSolana = (fromNetwork.id === 'ethereum' || fromNetwork.id === 'base') && toNetwork.id === 'solana';

    if (isSolanaToEvm) {
      if (!solanaWallet.connected || !solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet first.");
      }
      if (!ethAddress && !bridgeState.customEthAddress) {
        throw new Error("Please connect your Ethereum wallet or enter a recipient address.");
      }
      if (bridgeState.customEthAddress && !isValidEthereumAddress(bridgeState.customEthAddress)) {
        throw new Error("Please enter a valid Ethereum address.");
      }
    } else if (isEvmToSolana) {
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
      if (!solanaWallet.publicKey && !bridgeState.customSolanaAddress) {
        throw new Error("Please connect your Solana wallet or enter a recipient address.");
      }
      if (bridgeState.customSolanaAddress && !isValidSolanaAddress(bridgeState.customSolanaAddress)) {
        throw new Error("Please enter a valid Solana address.");
      }
    } else {
      // EVM to EVM
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new Error("Please enter a valid amount.");
    }

    // Check if amount exceeds balance
    if (fromNetwork.id === 'solana') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      if (amountNum > solanaBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.solanaBalance || '0'} tokens.`);
      }
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      if (amountNum > ethereumBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.ethereumBalance || '0'} tokens.`);
      }
    }
  }, [fromNetwork.id, toNetwork.id, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, amount, bridgeState.customEthAddress, bridgeState.customSolanaAddress, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  // Unified quote function using bridge service
  const quoteBridge = useCallback(async () => {
    try {
      validateInputs();

      if (!bridgeService.isSupported()) {
        throw new Error(`Bridge from ${fromNetwork.name} to ${toNetwork.name} is not supported yet`);
      }

      setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

      const recipientAddress = toNetwork.id === 'solana'
        ? (solanaWallet.publicKey?.toString() || bridgeState.customSolanaAddress)
        : (ethAddress || bridgeState.customEthAddress);

      if (!recipientAddress) {
        throw new Error("Recipient address is required");
      }

      const quote = await bridgeService.quoteBridge(
        amount,
        recipientAddress,
        solanaWallet,
        ethAddress
      );

      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        nativeFee: BigInt(quote.nativeFee),
        receiveAmount: quote.receiveAmount,
        gasPrice: quote.gasPrice ? {
          maxFeePerGas: BigInt(quote.gasPrice),
          maxPriorityFeePerGas: BigInt(quote.gasPrice)
        } : null,
      }));

    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to get quote',
      }));
    }
  }, [bridgeService, fromNetwork.name, toNetwork.name, toNetwork.id, amount, validateInputs, solanaWallet, ethAddress, bridgeState.customEthAddress, bridgeState.customSolanaAddress]);

  // Unified execute function using bridge service
  const executeBridge = useCallback(async () => {
    try {
      if (!bridgeState.nativeFee) {
        throw new Error("Please get a quote first");
      }

      setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

      const recipientAddress = toNetwork.id === 'solana'
        ? (solanaWallet.publicKey?.toString() || bridgeState.customSolanaAddress)
        : (ethAddress || bridgeState.customEthAddress);

      if (!recipientAddress) {
        throw new Error("Recipient address is required");
      }

      const result = await bridgeService.executeBridge(
        amount,
        recipientAddress,
        bridgeState.nativeFee.toString(),
        solanaWallet,
        ethAddress,
        writeOftContract
      );

      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        txHash: result.txHash,
        layerZeroScanLink: result.layerZeroScanLink || null,
      }));

      // Add to transaction history
      const newTransaction = {
        txHash: result.txHash,
        timestamp: Date.now(),
        fromChain: fromNetwork.id as 'solana' | 'ethereum',
        toChain: toNetwork.id as 'solana' | 'ethereum',
        amount: `${amount} USDUC`,
        status: 'confirmed' as const,
        layerZeroScanLink: result.layerZeroScanLink,
      };

      const existingTransactions = JSON.parse(localStorage.getItem('bridgeTransactions') || '[]');
      const updatedTransactions = [newTransaction, ...existingTransactions].slice(0, 10);
      localStorage.setItem('bridgeTransactions', JSON.stringify(updatedTransactions));
      setTransactions(updatedTransactions);

    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to execute bridge',
      }));
    }
  }, [bridgeService, bridgeState.nativeFee, toNetwork.id, fromNetwork.id, amount, solanaWallet, ethAddress, bridgeState.customEthAddress, bridgeState.customSolanaAddress, writeOftContract]);









  const hasInsufficientBalance = useMemo(() => {
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) return false;

    if (fromNetwork.id === 'solana') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      return amountNum > solanaBalance;
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      return amountNum > ethereumBalance;
    }
  }, [fromNetwork.id, amount, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const canQuote = useMemo(() => {
    const isSolanaToEvm = fromNetwork.id === 'solana' && (toNetwork.id === 'ethereum' || toNetwork.id === 'base');
    const isEvmToSolana = (fromNetwork.id === 'ethereum' || fromNetwork.id === 'base') && toNetwork.id === 'solana';
    const isEvmToEvm = (fromNetwork.id === 'ethereum' || fromNetwork.id === 'base') && (toNetwork.id === 'ethereum' || toNetwork.id === 'base');

    let basicRequirements = false;
    if (isSolanaToEvm) {
      basicRequirements = solanaWallet.connected && !!(ethAddress || bridgeState.customEthAddress);
    } else if (isEvmToSolana) {
      basicRequirements = isEthConnected && !!(solanaWallet.publicKey || bridgeState.customSolanaAddress);
    } else if (isEvmToEvm) {
      basicRequirements = isEthConnected;
    }

    return basicRequirements && !hasInsufficientBalance;
  }, [fromNetwork.id, toNetwork.id, solanaWallet.connected, ethAddress, isEthConnected, solanaWallet.publicKey, bridgeState.customEthAddress, bridgeState.customSolanaAddress, hasInsufficientBalance]);

  const clearTransactionHistory = useCallback(() => {
    setTransactions([]);
  }, []);

  if (!isClient) return null;

  return (
    <div className="w-full">
      {/* Main Bridge Interface */}
      <div className="relative pt-8 md:pt-40">


        {/* Network Selection */}
        <div className="bg-white rounded-2xl p-3 md:p-8 shadow-lg border-2 border-[#2b72d6] max-w-5xl mx-auto space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* From Network */}
            <div>
              <NetworkSelector
                selectedNetwork={fromNetwork}
                onNetworkChange={setFromNetwork}
                excludeNetwork={toNetwork}
                label="From Network"
              />
              <div className="mt-4 bg-white rounded-xl p-3 md:p-4 border-2 border-[#2b72d6]">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-black text-sm font-medium">Amount</span>
                  <span className="text-black text-sm">
                    Balance: {fromNetwork.id === 'solana' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
                  </span>
                </div>
                <div className="flex items-center justify-between gap-3">
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0.0"
                    className="bg-transparent text-black text-2xl font-semibold outline-none flex-1 min-w-0 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                    step="0.000001"
                    min="0"
                  />
                  <div className="flex items-center space-x-2 bg-white border-2 border-[#2b72d6] rounded-lg px-3 py-2 flex-shrink-0">
                    <span className="text-black font-medium text-sm">USDUC</span>
                  </div>
                </div>
              </div>
            </div>

            {/* To Network */}
            <div>
              <NetworkSelector
                selectedNetwork={toNetwork}
                onNetworkChange={setToNetwork}
                excludeNetwork={fromNetwork}
                label="To Network"
              />
              <div className="mt-4 bg-white rounded-xl p-3 md:p-4 border-2 border-[#2b72d6]">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-black text-sm font-medium">You will receive</span>
                  <span className="text-black text-sm">
                    Balance: {toNetwork.id === 'solana' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
                  </span>
                </div>
                <div className="flex items-center justify-between gap-3">
                  <div className="text-black text-2xl font-semibold flex-1 min-w-0 overflow-hidden text-ellipsis">
                    {bridgeState.receiveAmount || '0.0'}
                  </div>
                  <div className="flex items-center space-x-2 bg-white border-2 border-[#2b72d6] rounded-lg px-3 py-2 flex-shrink-0">
                    <span className="text-black font-medium text-sm">USDUC</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Swap Networks Button */}
          <div className="flex justify-center">
            <button
              onClick={() => {
                const temp = fromNetwork;
                setFromNetwork(toNetwork);
                setToNetwork(temp);
                resetBridgeState();
              }}
              className="bg-[#2b72d6] hover:bg-[#1e5bb8] rounded-full p-3 transition-colors border-2 border-[#2b72d6]"
            >
              <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m-4 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </button>
          </div>
        </div>

        {/* Wallet Connection and Action Section */}
        <div className="mt-4 md:mt-6 max-w-5xl mx-auto space-y-4 md:space-y-6">
          {/* Wallet Connection Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
            {/* Solana Wallet */}
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border-2 border-[#2b72d6]">
              <div className="flex items-center space-x-3">
                <img src="/solana-sol-logo.svg" alt="Solana" className="w-6 h-6" />
                <span className="text-black font-medium">Solana</span>
              </div>
              {solanaWallet.connected ? (
                <div className="flex items-center space-x-2">
                  <span className="text-green-600 text-sm font-medium">
                    {solanaWallet.publicKey?.toString().slice(0, 4)}...{solanaWallet.publicKey?.toString().slice(-4)}
                  </span>
                  <button
                    onClick={() => solanaWallet.disconnect()}
                    className="text-black hover:text-gray-600 text-sm"
                  >
                    Disconnect
                  </button>
                </div>
              ) : (
                <WalletMultiButton className="!bg-[#2b72d6] !hover:bg-[#1e5bb8] !text-black !text-sm !py-1 !px-3 !rounded-lg !border-2 !border-[#2b72d6]" />
              )}
            </div>

            {/* Ethereum Wallet */}
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border-2 border-[#2b72d6]">
              <div className="flex items-center space-x-3">
                <img src="/ethereum-eth-logo.svg" alt="Ethereum" className="w-6 h-6" />
                <span className="text-black font-medium">Ethereum</span>
              </div>
              {isEthConnected ? (
                <div className="flex items-center space-x-2">
                  <span className="text-green-600 text-sm font-medium">
                    {ethAddress?.slice(0, 6)}...{ethAddress?.slice(-4)}
                  </span>
                  <button
                    onClick={() => disconnect()}
                    className="text-black hover:text-gray-600 text-sm"
                  >
                    Disconnect
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setIsEthWalletModalOpen(true)}
                  className="bg-[#2b72d6] hover:bg-[#1e5bb8] text-black text-sm py-2 px-4 rounded-lg transition-colors border-2 border-[#2b72d6]"
                >
                  Select Wallet
                </button>
              )}
            </div>
          </div>

          {/* Custom Address Inputs */}
          {fromNetwork.id === 'solana' && (toNetwork.id === 'ethereum' || toNetwork.id === 'base') && !ethAddress && (
            <div>
              <input
                type="text"
                value={bridgeState.customEthAddress}
                onChange={(e) => setBridgeState(prev => ({ ...prev, customEthAddress: e.target.value }))}
                className="w-full px-4 py-3 bg-white border-2 border-[#2b72d6] rounded-lg text-black placeholder-gray-500 focus:outline-none focus:border-[#1e5bb8]"
                placeholder={`Enter ${toNetwork.name} address (0x...)`}
              />
            </div>
          )}

          {(fromNetwork.id === 'ethereum' || fromNetwork.id === 'base') && toNetwork.id === 'solana' && !solanaWallet.publicKey && (
            <div>
              <input
                type="text"
                value={bridgeState.customSolanaAddress}
                onChange={(e) => setBridgeState(prev => ({ ...prev, customSolanaAddress: e.target.value }))}
                className="w-full px-4 py-3 bg-white border-2 border-[#2b72d6] rounded-lg text-black placeholder-gray-500 focus:outline-none focus:border-[#1e5bb8]"
                placeholder="Enter Solana address..."
              />
            </div>
          )}

          {/* Error Display */}
          {bridgeState.error && (
            <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md p-4 bg-white border-2 border-red-500 rounded-lg shadow-lg">
              <p className="text-sm text-red-600 font-medium">{bridgeState.error}</p>
            </div>
          )}

          {/* Action Button */}
          <div>
            {bridgeService.isSupported() ? (
              <button
                onClick={async () => {
                  if (!bridgeState.nativeFee) {
                    await quoteBridge();
                  } else {
                    await executeBridge();
                  }
                }}
                disabled={!canQuote || bridgeState.isLoading || !!bridgeState.txHash}
                className="w-full py-4 px-6 bg-[#2b72d6] hover:bg-[#1e5bb8] disabled:bg-gray-400 disabled:cursor-not-allowed text-black font-semibold rounded-xl transition-all duration-200 flex items-center justify-center border-2 border-[#2b72d6] min-h-[64px]"
              >
                {bridgeState.isLoading ? (
                  <>
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>{bridgeState.nativeFee ? 'Sending...' : 'Getting Quote...'}</span>
                    </div>
                  </>
                ) : bridgeState.txHash ? (
                  <div className="flex items-center gap-3">
                    <span>Tokens bridged!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-300 hover:text-blue-200"
                        onClick={(e) => e.stopPropagation()}
                      >
                        View transaction ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex items-center gap-3">
                    <span>Bridge Tokens</span>
                    <span className="text-sm text-gray-300">
                      {(Number(bridgeState.nativeFee) / 1e9).toFixed(6)} SOL Fee
                    </span>
                  </div>
                ) : (
                  <span>Get Quote & Bridge</span>
                )}
              </button>
            ) : (
              <button
                disabled={true}
                className="w-full py-4 px-6 bg-gray-400 disabled:cursor-not-allowed text-black font-semibold rounded-xl transition-all duration-200 flex items-center justify-center border-2 border-gray-400 min-h-[64px]"
              >
                <span>{`Bridge from ${fromNetwork.name} to ${toNetwork.name} coming soon`}</span>
              </button>
            )}
          </div>

          {/* Transaction History Button & Panel */}
          {transactions.length > 0 && (
            <div className="mt-4 md:mt-8 text-center">
              <button
                onClick={() => setIsHistoryVisible(!isHistoryVisible)}
                className="text-sm text-[#2b72d6] hover:text-[#1e5bb8] flex items-center gap-2 mx-auto font-medium"
              >
                <svg
                  className={`w-4 h-4 transition-transform ${isHistoryVisible ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
                {isHistoryVisible ? 'Hide' : 'View'} Transaction History ({transactions.length})
              </button>

              {isHistoryVisible && (
                <div className="mt-3 md:mt-4 max-w-3xl mx-auto bg-white rounded-xl p-3 md:p-4 border-2 border-[#2b72d6] text-left space-y-2 md:space-y-3">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-black font-semibold">Transaction History</h4>
                    <button
                      onClick={clearTransactionHistory}
                      className="text-xs text-red-600 hover:text-red-500 font-medium"
                    >
                      Clear All
                    </button>
                  </div>

                  {transactions.map((tx, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-white rounded-lg border-2 border-[#2b72d6]"
                    >
                      <div className="flex items-center gap-3">
                        <img
                          src={tx.fromChain === 'solana' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                          alt={tx.fromChain}
                          className="w-5 h-5"
                        />
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                        <img
                          src={tx.toChain === 'solana' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                          alt={tx.toChain}
                          className="w-5 h-5"
                        />
                        <span className="text-black text-sm font-medium">{tx.amount}</span>
                      </div>
                      <a
                        href={tx.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm text-[#2b72d6] hover:text-[#1e5bb8] font-medium"
                      >
                        <span>LayerZero Scan</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Powered by LayerZero */}
          <div className="flex items-center justify-center pt-2 md:pt-4">
            <div className="flex items-center space-x-2">
              <span className="text-gray-600 text-sm">Powered by</span>
              <img
                src="/layerzero.svg"
                alt="LayerZero"
                className="h-5 md:h-6 opacity-80"
              />
            </div>
          </div>
        </div>
      </div>

      {
        isEthWalletModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]">
            <div className="bg-white rounded-2xl p-6 max-w-sm w-full mx-4 border-2 border-[#2b72d6] relative">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-black">Connect Ethereum Wallet</h3>
                <button
                  onClick={() => setIsEthWalletModalOpen(false)}
                  className="text-gray-600 hover:text-black"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-3">
                {connectors.map((connector) => (
                  <button
                    key={connector.uid}
                    onClick={() => {
                      connect({ connector });
                      setIsEthWalletModalOpen(false);
                    }}
                    disabled={isConnectPending}
                    className="w-full flex items-center space-x-3 p-3 bg-white hover:bg-gray-50 rounded-lg border-2 border-[#2b72d6] transition-colors disabled:opacity-50"
                  >
                    <div className="w-8 h-8 bg-[#2b72d6] rounded-full flex items-center justify-center">
                      <span className="text-black text-sm font-bold">
                        {connector.name.charAt(0)}
                      </span>
                    </div>
                    <span className="text-black font-medium">{connector.name}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )
      }
    </div >
  );
}
